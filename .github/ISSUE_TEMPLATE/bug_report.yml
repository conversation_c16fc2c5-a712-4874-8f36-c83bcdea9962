name: Bug Report
title: "[Bug]: "
labels: ["bug"]
description: Report broken or incorrect behaviour
body:
  - type: markdown
    attributes:
      value: >
        Thanks for taking the time to fill out a bug.
        Please note that this form is for bugs only!
  - type: textarea
    id: what-happened
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction Steps
      description: >
         What you did to make it happen. Include any request payloads, API calls, or specific commands used.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
      description: >
         A clear and concise description of what you expected to happen.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Screenshots and relevant files
      description: >
         If applicable, add screenshots or relevant files to help explain your problem.
    validations:
      required: false
  - type: dropdown
    id: platform
    attributes:
      label: "Platform"
      description: "Select the platform where you encountered this bug"
      options:
        - "Google Cloud Platform"
        - "Digital Ocean"
        - "Local"
        - "Other"
    validations:
      required: true
  - type: dropdown
    id: assign
    attributes:
      label: "Would you like to work on this issue?"
      options:
        - "Yes"
  - type: checkboxes
    attributes:
      label: Checklist
      description: >
        Let's make sure you've properly done due diligence when reporting this issue!
      options:
        - label: I have searched the open issues for duplicates.
          required: true
        - label: I have shown the entire traceback, if possible.
          required: true
  - type: textarea
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
